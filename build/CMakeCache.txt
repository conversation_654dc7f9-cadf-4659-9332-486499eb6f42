# This is the CMakeCache file.
# For build in directory: /home/<USER>/S1_robot/build
# It was generated by CMake: /usr/local/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Builds the googlemock subproject
BUILD_GMOCK:BOOL=ON

//Build dynamically-linked binaries
BUILD_SHARED_LIBS:BOOL=ON

//Boost atomic library (debug)
Boost_ATOMIC_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_atomic.so

//Boost atomic library (release)
Boost_ATOMIC_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_atomic.so

//Boost chrono library (debug)
Boost_CHRONO_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_chrono.so

//Boost chrono library (release)
Boost_CHRONO_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_chrono.so

//Boost date_time library (debug)
Boost_DATE_TIME_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_date_time.so

//Boost date_time library (release)
Boost_DATE_TIME_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_date_time.so

//Boost filesystem library (debug)
Boost_FILESYSTEM_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_filesystem.so

//Boost filesystem library (release)
Boost_FILESYSTEM_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_filesystem.so

//Path to a file.
Boost_INCLUDE_DIR:PATH=/home/<USER>/miniconda3/envs/coal/include

//Boost library directory DEBUG
Boost_LIBRARY_DIR_DEBUG:PATH=/home/<USER>/miniconda3/envs/coal/lib

//Boost library directory RELEASE
Boost_LIBRARY_DIR_RELEASE:PATH=/home/<USER>/miniconda3/envs/coal/lib

//Boost python313 library (debug)
Boost_PYTHON313_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_python313.so

//Boost python313 library (release)
Boost_PYTHON313_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_python313.so

//Boost serialization library (debug)
Boost_SERIALIZATION_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_serialization.so

//Boost serialization library (release)
Boost_SERIALIZATION_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_serialization.so

//Boost system library (debug)
Boost_SYSTEM_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_system.so

//Boost system library (release)
Boost_SYSTEM_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_system.so

//Boost thread library (debug)
Boost_THREAD_LIBRARY_DEBUG:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_thread.so

//Boost thread library (release)
Boost_THREAD_LIBRARY_RELEASE:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libboost_thread.so

//List of ';' separated packages to exclude
CATKIN_BLACKLIST_PACKAGES:STRING=

//catkin devel space
CATKIN_DEVEL_PREFIX:PATH=/home/<USER>/S1_robot/devel

//Catkin enable testing
CATKIN_ENABLE_TESTING:BOOL=ON

//Catkin skip testing
CATKIN_SKIP_TESTING:BOOL=OFF

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
CATKIN_SYMLINK_INSTALL:BOOL=OFF

//List of ';' separated packages to build
CATKIN_WHITELIST_PACKAGES:STRING=

//The directory containing a CMake configuration file for CLI11.
CLI11_DIR:PATH=/home/<USER>/miniconda3/envs/coal/share/cmake/CLI11

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-13

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-13

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/S1_robot/install

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Project

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.10.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=10

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
DL_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
DL_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libdl.so

//Path to a program.
DOXYGEN_EXECUTABLE:FILEPATH=DOXYGEN_EXECUTABLE-NOTFOUND

//Path to a program.
EMPY_EXECUTABLE:FILEPATH=EMPY_EXECUTABLE-NOTFOUND

//Empy script
EMPY_SCRIPT:STRING=/usr/lib/python3/dist-packages/em.py

//Set CMAKE_CXX_STANDARD if a dependency require it
ENFORCE_MINIMAL_CXX_STANDARD:BOOL=OFF

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/lib/cmake/eigen3

//Path to a library.
GMOCK_LIBRARY:FILEPATH=GMOCK_LIBRARY-NOTFOUND

//Path to a library.
GMOCK_LIBRARY_DEBUG:FILEPATH=GMOCK_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
GMOCK_MAIN_LIBRARY:FILEPATH=GMOCK_MAIN_LIBRARY-NOTFOUND

//Path to a library.
GMOCK_MAIN_LIBRARY_DEBUG:FILEPATH=GMOCK_MAIN_LIBRARY_DEBUG-NOTFOUND

//The directory containing a CMake configuration file for GMock.
GMock_DIR:PATH=GMock_DIR-NOTFOUND

//Path to a file.
GTEST_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GTEST_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgtest.a

//Path to a library.
GTEST_LIBRARY_DEBUG:FILEPATH=GTEST_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
GTEST_MAIN_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgtest_main.a

//Path to a library.
GTEST_MAIN_LIBRARY_DEBUG:FILEPATH=GTEST_MAIN_LIBRARY_DEBUG-NOTFOUND

//The directory containing a CMake configuration file for GTest.
GTest_DIR:PATH=GTest_DIR-NOTFOUND

//Compile with a sanitizer. Options are: Address, Memory, MemoryWithOrigins,
// Undefined, Thread, Leak, 'Address;Undefined', CFI
GZ_SANITIZER:STRING=

//Deprecated. Please use [GZ_SANITIZER]. Compile with a sanitizer.
// Options are: Address, Memory, MemoryWithOrigins, Undefined,
// Thread, Leak, 'Address;Undefined', CFI
IGN_SANITIZER:STRING=

//Enable installation of googletest. (Projects embedding googletest
// may want to turn this OFF.)
INSTALL_GTEST:BOOL=OFF

//lsb_release executable was found
LSB_FOUND:BOOL=TRUE

//Path to a program.
LSB_RELEASE_EXECUTABLE:FILEPATH=/usr/bin/lsb_release

//Path to a program.
NOSETESTS:FILEPATH=/usr/bin/nosetests3

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-fopenmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=gomp;pthread

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-fopenmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=gomp;pthread

//Path to the gomp library for OpenMP
OpenMP_gomp_LIBRARY:FILEPATH=/usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so

//Path to the pthread library for OpenMP
OpenMP_pthread_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpthread.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Enable Debian-style Python package layout
PYTHON_DEB_LAYOUT:BOOL=OFF

//Path to a program.
PYTHON_EXECUTABLE:FILEPATH=/usr/bin/python3

//Enable standard Python package layout
PYTHON_STANDARD_LAYOUT:BOOL=ON

//Specify specific Python version to use ('major.minor' or 'major')
PYTHON_VERSION:STRING=3

//Location of Python module em
PY_EM:STRING=/usr/lib/python3/dist-packages/em.py

//The directory containing a CMake configuration file for Pinocchio.
Pinocchio_DIR:PATH=Pinocchio_DIR-NOTFOUND

//Value Computed by CMake
Project_BINARY_DIR:STATIC=/home/<USER>/S1_robot/build

//Value Computed by CMake
Project_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=/home/<USER>/S1_robot/src

//The directory containing a CMake configuration file for Qhull.
Qhull_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/Qhull

//Path to a library.
RT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/librt.so

//The directory containing a CMake configuration file for SDFormat14.
SDFormat14_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/sdformat14

//Enable debian style python package layout
SETUPTOOLS_DEB_LAYOUT:BOOL=ON

//Name of the computer/site where compile is being run
SITE:STRING=lvh-IdeaCentre-GeekPro-17IRB

//Path to a library.
TINYXML2_LIBRARY_tinyxml2:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libtinyxml2.so

//LSB Distrib tag
UBUNTU:BOOL=TRUE

//LSB Distrib - codename tag
UBUNTU_FOCAL:BOOL=TRUE

//Path to a file.
_gmock_INCLUDES:FILEPATH=/usr/src/googletest/googlemock/include/gmock/gmock.h

//Path to a file.
_gmock_SOURCES:FILEPATH=/usr/src/gmock/src/gmock.cc

//Path to a file.
_gtest_INCLUDES:FILEPATH=/usr/include/gtest/gtest.h

//Path to a file.
_gtest_SOURCES:FILEPATH=/usr/src/gtest/src/gtest.cc

//The directory containing a CMake configuration file for casadi.
casadi_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/casadi

//The directory containing a CMake configuration file for catkin.
catkin_DIR:PATH=/opt/ros/noetic/share/catkin/cmake

//The directory containing a CMake configuration file for class_loader.
class_loader_DIR:PATH=/opt/ros/noetic/share/class_loader/cmake

//The directory containing a CMake configuration file for coal.
coal_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/coal

//The directory containing a CMake configuration file for console_bridge.
console_bridge_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/console_bridge/cmake

//The directory containing a CMake configuration file for cpp_common.
cpp_common_DIR:PATH=/opt/ros/noetic/share/cpp_common/cmake

//The directory containing a CMake configuration file for eigen_conversions.
eigen_conversions_DIR:PATH=/opt/ros/noetic/share/eigen_conversions/cmake

//The directory containing a CMake configuration file for eigenpy.
eigenpy_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/eigenpy

//The directory containing a CMake configuration file for gencpp.
gencpp_DIR:PATH=/opt/ros/noetic/share/gencpp/cmake

//The directory containing a CMake configuration file for geneus.
geneus_DIR:PATH=/opt/ros/noetic/share/geneus/cmake

//The directory containing a CMake configuration file for genlisp.
genlisp_DIR:PATH=/opt/ros/noetic/share/genlisp/cmake

//The directory containing a CMake configuration file for genmsg.
genmsg_DIR:PATH=/opt/ros/noetic/share/genmsg/cmake

//The directory containing a CMake configuration file for gennodejs.
gennodejs_DIR:PATH=/opt/ros/noetic/share/gennodejs/cmake

//The directory containing a CMake configuration file for genpy.
genpy_DIR:PATH=/opt/ros/noetic/share/genpy/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/opt/ros/noetic/share/geometry_msgs/cmake

//Value Computed by CMake
gmock_BINARY_DIR:STATIC=/home/<USER>/S1_robot/build/gtest/googlemock

//Value Computed by CMake
gmock_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
gmock_LIB_DEPENDS:STATIC=general;gtest;

//Value Computed by CMake
gmock_SOURCE_DIR:STATIC=/usr/src/googletest/googlemock

//Build all of Google Mock's own tests.
gmock_build_tests:BOOL=OFF

//Dependencies for the target
gmock_main_LIB_DEPENDS:STATIC=general;gmock;

//Value Computed by CMake
googletest-distribution_BINARY_DIR:STATIC=/home/<USER>/S1_robot/build/gtest

//Value Computed by CMake
googletest-distribution_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
googletest-distribution_SOURCE_DIR:STATIC=/usr/src/googletest

//Value Computed by CMake
gtest_BINARY_DIR:STATIC=/home/<USER>/S1_robot/build/gtest/googletest

//Value Computed by CMake
gtest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
gtest_SOURCE_DIR:STATIC=/usr/src/googletest/googletest

//Build gtest's sample programs.
gtest_build_samples:BOOL=OFF

//Build all of gtest's own tests.
gtest_build_tests:BOOL=OFF

//Disable uses of pthreads in gtest.
gtest_disable_pthreads:BOOL=OFF

//Use shared (DLL) run-time lib even when Google Test is built
// as static lib.
gtest_force_shared_crt:BOOL=OFF

//Build gtest with internal symbols hidden in shared libraries.
gtest_hide_internal_symbols:BOOL=OFF

//Dependencies for the target
gtest_main_LIB_DEPENDS:STATIC=general;gtest;

//The directory containing a CMake configuration file for gz-cmake3.
gz-cmake3_DIR:PATH=/home/<USER>/miniconda3/envs/coal/share/cmake/gz-cmake3

//The directory containing a CMake configuration file for gz-math7.
gz-math7_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/gz-math7

//The directory containing a CMake configuration file for gz-utils2-cli.
gz-utils2-cli_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/gz-utils2-cli

//The directory containing a CMake configuration file for gz-utils2.
gz-utils2_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/gz-utils2

//The directory containing a CMake configuration file for hpp-fcl.
hpp-fcl_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/hpp-fcl

//The directory containing a CMake configuration file for kdl_parser.
kdl_parser_DIR:PATH=/opt/ros/noetic/share/kdl_parser/cmake

//Path to a library.
lib:FILEPATH=/usr/lib/x86_64-linux-gnu/libnlopt.so

//The directory containing a CMake configuration file for message_generation.
message_generation_DIR:PATH=/opt/ros/noetic/share/message_generation/cmake

//The directory containing a CMake configuration file for message_runtime.
message_runtime_DIR:PATH=/opt/ros/noetic/share/message_runtime/cmake

//The directory containing a CMake configuration file for octomap.
octomap_DIR:PATH=/opt/ros/noetic/share/octomap

//Path to a library.
onelib:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/liburdfdom_world.so

//Path to a library.
onelibd:FILEPATH=onelibd-NOTFOUND

//The directory containing a CMake configuration file for orocos_kdl.
orocos_kdl_DIR:PATH=/usr/share/orocos_kdl/cmake

//The directory containing a CMake configuration file for pinocchio.
pinocchio_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/cmake/pinocchio

//Path to a library.
pkgcfg_lib_TINYXML2_tinyxml2:FILEPATH=/home/<USER>/miniconda3/envs/coal/lib/libtinyxml2.so

//The directory containing a CMake configuration file for pluginlib.
pluginlib_DIR:PATH=/opt/ros/noetic/share/pluginlib/cmake

//Value Computed by CMake
robot_controller_BINARY_DIR:STATIC=/home/<USER>/S1_robot/build/robot_controller

//Value Computed by CMake
robot_controller_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
robot_controller_SOURCE_DIR:STATIC=/home/<USER>/S1_robot/src/robot_controller

//The directory containing a CMake configuration file for rosconsole.
rosconsole_DIR:PATH=/opt/ros/noetic/share/rosconsole/cmake

//The directory containing a CMake configuration file for rosconsole_bridge.
rosconsole_bridge_DIR:PATH=/opt/ros/noetic/share/rosconsole_bridge/cmake

//The directory containing a CMake configuration file for roscpp.
roscpp_DIR:PATH=/opt/ros/noetic/share/roscpp/cmake

//The directory containing a CMake configuration file for roscpp_serialization.
roscpp_serialization_DIR:PATH=/opt/ros/noetic/share/roscpp_serialization/cmake

//The directory containing a CMake configuration file for roscpp_traits.
roscpp_traits_DIR:PATH=/opt/ros/noetic/share/roscpp_traits/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/opt/ros/noetic/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for roslib.
roslib_DIR:PATH=/opt/ros/noetic/share/roslib/cmake

//The directory containing a CMake configuration file for rospack.
rospack_DIR:PATH=/opt/ros/noetic/share/rospack/cmake

//The directory containing a CMake configuration file for rospy.
rospy_DIR:PATH=/opt/ros/noetic/share/rospy/cmake

//The directory containing a CMake configuration file for rostime.
rostime_DIR:PATH=/opt/ros/noetic/share/rostime/cmake

//Dependencies for the target
self_lib_LIB_DEPENDS:STATIC=general;/opt/ros/noetic/lib/libeigen_conversions.so;general;/opt/ros/noetic/lib/libkdl_parser.so;general;/usr/lib/liborocos-kdl.so;general;/opt/ros/noetic/lib/liburdf.so;general;/usr/lib/x86_64-linux-gnu/liburdfdom_sensor.so;general;/usr/lib/x86_64-linux-gnu/liburdfdom_model_state.so;general;/usr/lib/x86_64-linux-gnu/liburdfdom_model.so;general;/usr/lib/x86_64-linux-gnu/liburdfdom_world.so;general;/usr/lib/x86_64-linux-gnu/libtinyxml.so;general;/opt/ros/noetic/lib/libclass_loader.so;general;/usr/lib/x86_64-linux-gnu/libPocoFoundation.so;general;/usr/lib/x86_64-linux-gnu/libdl.so;general;/opt/ros/noetic/lib/libroslib.so;general;/opt/ros/noetic/lib/librospack.so;general;/usr/lib/x86_64-linux-gnu/libpython3.8.so;general;/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libtinyxml2.so;general;/opt/ros/noetic/lib/librosconsole_bridge.so;general;/opt/ros/noetic/lib/libroscpp.so;general;/usr/lib/x86_64-linux-gnu/libpthread.so;general;/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0;general;/opt/ros/noetic/lib/librosconsole.so;general;/opt/ros/noetic/lib/librosconsole_log4cxx.so;general;/opt/ros/noetic/lib/librosconsole_backend_interface.so;general;/usr/lib/x86_64-linux-gnu/liblog4cxx.so;general;/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0;general;/opt/ros/noetic/lib/libroscpp_serialization.so;general;/opt/ros/noetic/lib/libxmlrpcpp.so;general;/opt/ros/noetic/lib/librostime.so;general;/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0;general;/opt/ros/noetic/lib/libcpp_common.so;general;/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4;

//The directory containing a CMake configuration file for serial.
serial_DIR:PATH=/opt/ros/noetic/share/serial/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/opt/ros/noetic/share/std_msgs/cmake

//The directory containing a CMake configuration file for tinyxml2_vendor.
tinyxml2_vendor_DIR:PATH=tinyxml2_vendor_DIR-NOTFOUND

//The directory containing a CMake configuration file for trac_ik_lib.
trac_ik_lib_DIR:PATH=/opt/ros/noetic/share/trac_ik_lib/cmake

//The directory containing a CMake configuration file for urdf.
urdf_DIR:PATH=/opt/ros/noetic/share/urdf/cmake

//The directory containing a CMake configuration file for urdfdom.
urdfdom_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/urdfdom/cmake

//The directory containing a CMake configuration file for urdfdom_headers.
urdfdom_headers_DIR:PATH=/home/<USER>/miniconda3/envs/coal/lib/urdfdom_headers/cmake

//The directory containing a CMake configuration file for visualization_msgs.
visualization_msgs_DIR:PATH=/opt/ros/noetic/share/visualization_msgs/cmake

//The directory containing a CMake configuration file for xmlrpcpp.
xmlrpcpp_DIR:PATH=/opt/ros/noetic/share/xmlrpcpp/cmake

//The directory containing a CMake configuration file for yaml-cpp.
yaml-cpp_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/yaml-cpp


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_DEBUG
Boost_ATOMIC_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_RELEASE
Boost_ATOMIC_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_DEBUG
Boost_CHRONO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_RELEASE
Boost_CHRONO_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_DEBUG
Boost_DATE_TIME_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_RELEASE
Boost_DATE_TIME_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_DEBUG
Boost_FILESYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_RELEASE
Boost_FILESYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_DEBUG
Boost_LIBRARY_DIR_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_RELEASE
Boost_LIBRARY_DIR_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PYTHON313_LIBRARY_DEBUG
Boost_PYTHON313_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PYTHON313_LIBRARY_RELEASE
Boost_PYTHON313_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SERIALIZATION_LIBRARY_DEBUG
Boost_SERIALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SERIALIZATION_LIBRARY_RELEASE
Boost_SERIALIZATION_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_DEBUG
Boost_SYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_RELEASE
Boost_SYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_DEBUG
Boost_THREAD_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_RELEASE
Boost_THREAD_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//catkin environment
CATKIN_ENV:INTERNAL=/home/<USER>/S1_robot/build/catkin_generated/env_cached.sh
CATKIN_TEST_RESULTS_DIR:INTERNAL=/home/<USER>/S1_robot/build/test_results
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/S1_robot/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=23
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/local/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/local/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/local/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/local/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/S1_robot/src
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=5
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/local/share/cmake-3.23
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DL_INCLUDE_DIRS
DL_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DL_LIBRARIES
DL_LIBRARIES-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/home/<USER>/miniconda3/envs/coal/include][cfound components: python313 ][v1.86.0()]
//Details about finding DL
FIND_PACKAGE_MESSAGE_DETAILS_DL:INTERNAL=[true][v()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][c ][v4.5()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-fopenmp][/usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so][/usr/lib/x86_64-linux-gnu/libpthread.so][v4.5()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-fopenmp][/usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so][/usr/lib/x86_64-linux-gnu/libpthread.so][v4.5()]
//Details about finding PY_em
FIND_PACKAGE_MESSAGE_DETAILS_PY_em:INTERNAL=[/usr/lib/python3/dist-packages/em.py][v()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3][/usr/include/python3.8][/usr/lib/python3/dist-packages/numpy/core/include][cfound components: Interpreter Development.Module NumPy ][v3.8.10()]
//Details about finding PythonInterp
FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp:INTERNAL=[/usr/bin/python3][v3.8.10()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
GMOCK_FROM_SOURCE_FOUND:INTERNAL=TRUE
GMOCK_FROM_SOURCE_INCLUDE_DIRS:INTERNAL=/usr/src/googletest/googlemock/include
GMOCK_FROM_SOURCE_LIBRARIES:INTERNAL=gmock
GMOCK_FROM_SOURCE_LIBRARY_DIRS:INTERNAL=/home/<USER>/S1_robot/build/gmock
GMOCK_FROM_SOURCE_MAIN_LIBRARIES:INTERNAL=gmock_main
//ADVANCED property for variable: GMOCK_LIBRARY
GMOCK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_LIBRARY_DEBUG
GMOCK_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_MAIN_LIBRARY
GMOCK_MAIN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_MAIN_LIBRARY_DEBUG
GMOCK_MAIN_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
GTEST_FROM_SOURCE_FOUND:INTERNAL=TRUE
GTEST_FROM_SOURCE_INCLUDE_DIRS:INTERNAL=/usr/include
GTEST_FROM_SOURCE_LIBRARIES:INTERNAL=gtest
GTEST_FROM_SOURCE_LIBRARY_DIRS:INTERNAL=/home/<USER>/S1_robot/build/gtest
GTEST_FROM_SOURCE_MAIN_LIBRARIES:INTERNAL=gtest_main
//ADVANCED property for variable: GTEST_INCLUDE_DIR
GTEST_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY
GTEST_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY_DEBUG
GTEST_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY
GTEST_MAIN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY_DEBUG
GTEST_MAIN_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_fopenmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_fopenmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=201511
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=201511
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_gomp_LIBRARY
OpenMP_gomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_pthread_LIBRARY
OpenMP_pthread_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PYTHON_EXECUTABLE
PYTHON_EXECUTABLE-ADVANCED:INTERNAL=1
//This needs to be in PYTHONPATH when 'setup.py install' is called.
//  And it needs to match.  But setuptools won't tell us where
// it will install things.
PYTHON_INSTALL_DIR:INTERNAL=lib/python3/dist-packages
TINYXML2_CFLAGS:INTERNAL=-I/home/<USER>/miniconda3/envs/coal/include
TINYXML2_CFLAGS_I:INTERNAL=
TINYXML2_CFLAGS_OTHER:INTERNAL=
TINYXML2_INCLUDEDIR:INTERNAL=/home/<USER>/miniconda3/envs/coal/include
TINYXML2_INCLUDE_DIRS:INTERNAL=/home/<USER>/miniconda3/envs/coal/include
TINYXML2_LDFLAGS:INTERNAL=-L/home/<USER>/miniconda3/envs/coal/lib;-ltinyxml2
TINYXML2_LDFLAGS_OTHER:INTERNAL=
TINYXML2_LIBDIR:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib
TINYXML2_LIBRARIES:INTERNAL=tinyxml2
TINYXML2_LIBRARY_DIRS:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib
//ADVANCED property for variable: TINYXML2_LIBRARY_tinyxml2
TINYXML2_LIBRARY_tinyxml2-ADVANCED:INTERNAL=1
TINYXML2_LIBS:INTERNAL=
TINYXML2_LIBS_L:INTERNAL=
TINYXML2_LIBS_OTHER:INTERNAL=
TINYXML2_LIBS_PATHS:INTERNAL=
TINYXML2_MODULE_NAME:INTERNAL=tinyxml2
TINYXML2_PREFIX:INTERNAL=/home/<USER>/miniconda3/envs/coal
TINYXML2_STATIC_CFLAGS:INTERNAL=-I/home/<USER>/miniconda3/envs/coal/include
TINYXML2_STATIC_CFLAGS_I:INTERNAL=
TINYXML2_STATIC_CFLAGS_OTHER:INTERNAL=
TINYXML2_STATIC_INCLUDE_DIRS:INTERNAL=/home/<USER>/miniconda3/envs/coal/include
TINYXML2_STATIC_LDFLAGS:INTERNAL=-L/home/<USER>/miniconda3/envs/coal/lib;-ltinyxml2
TINYXML2_STATIC_LDFLAGS_OTHER:INTERNAL=
TINYXML2_STATIC_LIBDIR:INTERNAL=
TINYXML2_STATIC_LIBRARIES:INTERNAL=tinyxml2
TINYXML2_STATIC_LIBRARY_DIRS:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib
TINYXML2_STATIC_LIBS:INTERNAL=
TINYXML2_STATIC_LIBS_L:INTERNAL=
TINYXML2_STATIC_LIBS_OTHER:INTERNAL=
TINYXML2_STATIC_LIBS_PATHS:INTERNAL=
TINYXML2_VERSION:INTERNAL=11.0.0
TINYXML2_tinyxml2_INCLUDEDIR:INTERNAL=
TINYXML2_tinyxml2_LIBDIR:INTERNAL=
TINYXML2_tinyxml2_PREFIX:INTERNAL=
TINYXML2_tinyxml2_VERSION:INTERNAL=
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=atomic;chrono;date_time;filesystem;python313;serialization;system;thread
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=/home/<USER>/miniconda3/envs/coal/include
//Last used Boost_LIBRARY_DIR_DEBUG value.
_Boost_LIBRARY_DIR_DEBUG_LAST:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib
//Last used Boost_LIBRARY_DIR_RELEASE value.
_Boost_LIBRARY_DIR_RELEASE_LAST:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=ON
//Last used Boost_USE_STATIC_LIBS value.
_Boost_USE_STATIC_LIBS_LAST:INTERNAL=OFF
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/home/<USER>/S1_robot/install
_MINIMAL_CXX_STANDARD:INTERNAL=14
_Python3_DEVELOPMENT_MODULE_SIGNATURE:INTERNAL=5c4d899b694456d2130e67e0e2e103ee
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3
_Python3_INCLUDE_DIR:INTERNAL=/usr/include/python3.8
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;8;10;64;;cpython-38-x86_64-linux-gnu;/usr/lib/python3.8;/usr/lib/python3.8;/usr/lib/python3/dist-packages;/usr/lib/python3/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=0f3e53742e142b1d9e50e4ca5b901dd8
_Python3_NUMPY_SIGNATURE:INTERNAL=b55367aa3fe29a459a05126e9e638174
//Path to a file.
_Python3_NumPy_INCLUDE_DIR:INTERNAL=/usr/lib/python3/dist-packages/numpy/core/include
__pkg_config_arguments_TINYXML2:INTERNAL=tinyxml2
__pkg_config_checked_TINYXML2:INTERNAL=1
//Result of TRY_COMPILE
_cxx_standard_build_status:INTERNAL=TRUE
//Result of TRY_RUN
_cxx_standard_run_status:INTERNAL=0
//ADVANCED property for variable: gmock_build_tests
gmock_build_tests-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_build_samples
gtest_build_samples-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_build_tests
gtest_build_tests-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_disable_pthreads
gtest_disable_pthreads-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_force_shared_crt
gtest_force_shared_crt-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_hide_internal_symbols
gtest_hide_internal_symbols-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_TINYXML2_tinyxml2
pkgcfg_lib_TINYXML2_tinyxml2-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/home/<USER>/miniconda3/envs/coal/lib

