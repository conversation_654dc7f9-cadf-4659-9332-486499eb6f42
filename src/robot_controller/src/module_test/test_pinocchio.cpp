#include "robot_controller/def_class.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "test_pinocchio");
    ros::NodeHandle nh;

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model);

    pinocchio::Data data(model);

    std::vector<pinocchio::JointIndex> right_arm_joints;
    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) // 0 是 root
    {
        const auto &joint = model.joints[j];
        const auto &name = model.names[j];
        std::cout << "joint name: " << name << " nv: " << joint.nv() << std::endl;
        joint.

        if(!is_mimic && joint.nv() > 0 && name.substr(0,2) == "r_joint")
            right_arm_joints.push_back(j);
    }

    std::cout << "右臂主动关节: ";
    for(auto j: right_arm_joints)
        std::cout << model.names[j] << " ";
    std::cout << std::endl;

    // 计算右臂自由度
    int right_arm_dof = 0;
    for(const auto &j: right_arm_joints)
    {
        const auto &joint = model.joints[j];
        right_arm_dof += joint.nv();   // 每个关节的自由度
    }
    std::cout << "右臂自由度: " << right_arm_dof << std::endl;
    return 0;
}
