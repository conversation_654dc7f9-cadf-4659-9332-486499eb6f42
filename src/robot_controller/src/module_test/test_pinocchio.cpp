#include "robot_controller/def_class.h"

KINEMATICS::SpatialTransform space_trans;

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"test_pinocchio");
    ros::NodeHandle nh;

    // 1. 定义机器人模型
    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string urdf_filename = path_pkg + "/description/urdf/S1_robot.urdf";

    // 2. 从 URDF 构建模型
    pinocchio::urdf::buildModel(urdf_filename, model);
    std::cout << "Number of joints: " << model.njoints << std::endl;

    // 3. 创建数据结构
    pinocchio::Data data(model);

    // 4. 定义关节角向量
    Eigen::VectorXd q = Eigen::VectorXd::Zero(model.nq);

    // 5. 前向运动学
    pinocchio::forwardKinematics(model, data, q);

    // 6. 获取末端位姿
    const auto& oMf = data.oMf[model.getJointId("r_Link7")];
    std::cout << "End-effector position: " << oMf.translation().transpose() << std::endl;
    return 0;
}