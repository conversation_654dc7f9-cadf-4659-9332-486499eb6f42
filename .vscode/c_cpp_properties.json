{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": true}, "includePath": ["${workspaceFolder}/**", "/opt/ros/noetic/include/**", "/usr/include/**", "/usr/local/include/**", "${HOME}/S1_robot/src/robot_controller/include/**", "${HOME}/miniconda3/envs/coal/include", "${HOME}/miniconda3/envs/coal/include/coal", "${HOME}/miniconda3/envs/coal/include/eigen3", "${workspaceFolder}/devel/include", "/opt/ros/noetic/include"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}